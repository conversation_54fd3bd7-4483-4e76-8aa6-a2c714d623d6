<template>
  <ZPage title="组件测试页面" backgroundColor="#f5f5f5">
    <div class="test-index-container">
      <h2>组件测试页面索引</h2>
      <p class="description">这里包含了所有可用的组件测试页面，方便开发和调试。</p>

      <div class="test-list">
        <div v-for="testPage in testPages" :key="testPage.name" class="test-item">
          <div class="test-card" @click="navigateToTest(testPage.path)">
            <div class="test-icon">
              <ZIcon :type="testPage.icon" :size="32" color="#667eea" />
            </div>
            <div class="test-info">
              <h3 class="test-title">{{ testPage.title }}</h3>
              <p class="test-desc">{{ testPage.description }}</p>
              <div class="test-meta">
                <span class="test-path">{{ testPage.path }}</span>
                <span class="test-status" :class="testPage.status">{{ testPage.statusText }}</span>
              </div>
            </div>
            <div class="test-arrow">
              <ZIcon type="icon-arrow-right" :size="16" color="#999" />
            </div>
          </div>
        </div>
      </div>

      <div class="footer-info">
        <p>💡 提示：这些测试页面仅用于开发和调试，不会在生产环境中显示。</p>
      </div>
    </div>
  </ZPage>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();

// 测试页面列表
const testPages = ref([
  {
    name: "PublicActivityBonusTipTest",
    title: "PublicActivityBonusTip 组件测试",
    description: "测试活动奖励弹窗组件的各种功能和状态",
    path: "/test/public-activity-bonus-tip",
    icon: "icon-gift",
    status: "active",
    statusText: "可用",
  },
  {
    name: "CopyLinkTest",
    title: "CopyLinkTip 组件测试",
    description: "测试复制链接弹窗组件的功能",
    path: "/test/copylink-test",
    icon: "icon-link",
    status: "active",
    statusText: "可用",
  },
  {
    name: "CoinAnimationDemo",
    title: "金币动画演示",
    description: "演示金币动画效果和相关功能",
    path: "/test/coin",
    icon: "icon-coin",
    status: "active",
    statusText: "可用",
  },
  {
    name: "WalletRewardBtnTest",
    title: "钱包奖励按钮测试",
    description: "测试钱包奖励按钮的图片序列动画",
    path: "/test/wallet-reward-btn",
    icon: "icon-wallet",
    status: "active",
    statusText: "可用",
  },
  {
    name: "BonusWalletTest",
    title: "奖励钱包返回逻辑测试",
    description: "测试奖励钱包的返回逻辑和状态管理",
    path: "/test/bonus-wallet-test",
    icon: "icon-bonus",
    status: "active",
    statusText: "可用",
  },
  {
    name: "GradientButtonTest",
    title: "GradientButton 组件测试",
    description: "测试可配置渐变色按钮组件的各种样式和功能",
    path: "/test/gradient-button",
    icon: "icon-button",
    status: "active",
    statusText: "可用",
  },
]);

// 导航到测试页面
const navigateToTest = (path: string) => {
  router.push(path);
};
</script>

<style scoped lang="scss">
.test-index-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  h2 {
    color: #333;
    text-align: center;
    margin-bottom: 10px;
    font-size: 24px;
  }

  .description {
    text-align: center;
    color: #666;
    margin-bottom: 30px;
    font-size: 14px;
    line-height: 1.5;
  }

  .test-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .test-item {
    .test-card {
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      cursor: pointer;
      transition: all 0.2s ease;
      display: flex;
      align-items: center;
      gap: 16px;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      &:active {
        transform: translateY(0);
      }
    }

    .test-icon {
      flex-shrink: 0;
      width: 48px;
      height: 48px;
      background: linear-gradient(135deg, #f0f4ff 0%, #e8f2ff 100%);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .test-info {
      flex: 1;
      min-width: 0;

      .test-title {
        color: #333;
        font-size: 16px;
        font-weight: 600;
        margin: 0 0 8px 0;
      }

      .test-desc {
        color: #666;
        font-size: 14px;
        margin: 0 0 12px 0;
        line-height: 1.4;
      }

      .test-meta {
        display: flex;
        align-items: center;
        gap: 12px;
        flex-wrap: wrap;

        .test-path {
          color: #999;
          font-size: 12px;
          font-family: monospace;
          background: #f5f5f5;
          padding: 2px 6px;
          border-radius: 4px;
        }

        .test-status {
          font-size: 12px;
          padding: 2px 8px;
          border-radius: 12px;
          font-weight: 500;

          &.active {
            background: #e8f5e8;
            color: #2d8f2d;
          }

          &.inactive {
            background: #fff3cd;
            color: #856404;
          }

          &.deprecated {
            background: #f8d7da;
            color: #721c24;
          }
        }
      }
    }

    .test-arrow {
      flex-shrink: 0;
      opacity: 0.5;
      transition: opacity 0.2s ease;
    }

    .test-card:hover .test-arrow {
      opacity: 1;
    }
  }

  .footer-info {
    margin-top: 40px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    text-align: center;

    p {
      color: #666;
      font-size: 14px;
      margin: 0;
      line-height: 1.5;
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .test-index-container {
    padding: 16px;

    .test-item .test-card {
      padding: 16px;
      gap: 12px;

      .test-icon {
        width: 40px;
        height: 40px;
      }

      .test-info {
        .test-title {
          font-size: 15px;
        }

        .test-desc {
          font-size: 13px;
        }

        .test-meta {
          gap: 8px;

          .test-path {
            font-size: 11px;
          }

          .test-status {
            font-size: 11px;
          }
        }
      }
    }
  }
}
</style>
