<template>
  <ZPage title="GradientButton组件测试" backgroundColor="#f5f5f5">
    <div class="gradient-demo">
      <h2>可配置渐变色按钮演示</h2>

      <!-- 默认样式 -->
      <div class="demo-section">
        <h3>默认样式</h3>
        <GradientButton @click="handleClick">默认按钮</GradientButton>
      </div>

      <!-- 蓝色主题 -->
      <div class="demo-section">
        <h3>蓝色主题</h3>
        <GradientButton
          text="蓝色按钮"
          background-gradient="linear-gradient(90deg, #4facfe 0%, #00f2fe 100%)"
          border-gradient="linear-gradient(85deg, #e0f4ff 0%, #b3e5fc 50%, #81d4fa 100%)"
          @click="handleClick"
        />
      </div>

      <!-- 紫色主题 -->
      <div class="demo-section">
        <h3>紫色主题</h3>
        <GradientButton
          text="紫色按钮"
          background-gradient="linear-gradient(90deg, #a855f7 0%, #8b5cf6 100%)"
          border-gradient="linear-gradient(85deg, #f3e8ff 0%, #e9d5ff 50%, #ddd6fe 100%)"
          @click="handleClick"
        />
      </div>

      <!-- 绿色主题 -->
      <div class="demo-section">
        <h3>绿色主题</h3>
        <GradientButton
          text="绿色按钮"
          background-gradient="linear-gradient(90deg, #10b981 0%, #059669 100%)"
          border-gradient="linear-gradient(85deg, #d1fae5 0%, #a7f3d0 50%, #6ee7b7 100%)"
          @click="handleClick"
        />
      </div>

      <!-- 红色主题 -->
      <div class="demo-section">
        <h3>红色主题</h3>
        <GradientButton
          text="红色按钮"
          background-gradient="linear-gradient(90deg, #ef4444 0%, #dc2626 100%)"
          border-gradient="linear-gradient(85deg, #fee2e2 0%, #fecaca 50%, #fca5a5 100%)"
          @click="handleClick"
        />
      </div>

      <!-- 自定义禁用样式 -->
      <div class="demo-section">
        <h3>自定义禁用样式</h3>
        <GradientButton
          text="禁用按钮"
          background-gradient="linear-gradient(90deg, #4facfe 0%, #00f2fe 100%)"
          border-gradient="linear-gradient(85deg, #e0f4ff 0%, #b3e5fc 50%, #81d4fa 100%)"
          disabled-gradient="linear-gradient(135deg, #f1f5f9 0%, #cbd5e1 100%)"
          disabled
          @click="handleClick"
        />
      </div>

      <!-- 加载状态 -->
      <div class="demo-section">
        <h3>加载状态</h3>
        <GradientButton
          text="加载中..."
          background-gradient="linear-gradient(90deg, #a855f7 0%, #8b5cf6 100%)"
          border-gradient="linear-gradient(85deg, #f3e8ff 0%, #e9d5ff 50%, #ddd6fe 100%)"
          :loading="isLoading"
          @click="toggleLoading"
        />
      </div>

      <!-- 渐变方向演示 -->
      <div class="demo-section">
        <h3>不同渐变方向</h3>
        <div class="button-row">
          <GradientButton
            text="水平渐变"
            background-gradient="linear-gradient(90deg, #ff6b6b 0%, #feca57 100%)"
            border-gradient="linear-gradient(90deg, #ffe8e8 0%, #fff4e6 100%)"
            @click="handleClick"
          />
          <GradientButton
            text="垂直渐变"
            background-gradient="linear-gradient(180deg, #ff6b6b 0%, #feca57 100%)"
            border-gradient="linear-gradient(180deg, #ffe8e8 0%, #fff4e6 100%)"
            @click="handleClick"
          />
          <GradientButton
            text="对角渐变"
            background-gradient="linear-gradient(45deg, #ff6b6b 0%, #feca57 100%)"
            border-gradient="linear-gradient(45deg, #ffe8e8 0%, #fff4e6 100%)"
            @click="handleClick"
          />
        </div>
      </div>

      <!-- 事件日志 -->
      <div class="demo-section">
        <h3>事件日志</h3>
        <div class="event-log">
          <div v-for="(log, index) in eventLogs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-event">{{ log.event }}</span>
          </div>
          <div v-if="eventLogs.length === 0" class="no-logs">暂无事件日志</div>
        </div>
        <div class="log-controls">
          <button @click="clearLogs" class="clear-btn">清空日志</button>
        </div>
      </div>
    </div>
  </ZPage>
</template>

<script setup lang="ts">
import { ref } from "vue";
// @ts-ignore
import GradientButton from "@/components/GradientButton/index.vue";

const isLoading = ref(false);
const eventLogs = ref<Array<{ time: string; event: string }>>([]);

// 添加事件日志
const addLog = (event: string) => {
  const now = new Date();
  const time = now.toLocaleTimeString();
  eventLogs.value.unshift({ time, event });
  // 只保留最近10条日志
  if (eventLogs.value.length > 10) {
    eventLogs.value = eventLogs.value.slice(0, 10);
  }
};

const handleClick = () => {
  console.log("按钮被点击了！");
  addLog("按钮被点击");
};

const toggleLoading = () => {
  isLoading.value = !isLoading.value;
  addLog(`加载状态切换: ${isLoading.value ? "开始加载" : "停止加载"}`);
  
  if (isLoading.value) {
    setTimeout(() => {
      isLoading.value = false;
      addLog("加载完成（自动停止）");
    }, 3000);
  }
};

const clearLogs = () => {
  eventLogs.value = [];
  addLog("日志已清空");
};
</script>

<style lang="scss" scoped>
.gradient-demo {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
    font-size: 24px;
  }

  .demo-section {
    margin-bottom: 20px;
    padding: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h3 {
      margin-bottom: 15px;
      color: #555;
      font-size: 18px;
      border-bottom: 2px solid #f0f0f0;
      padding-bottom: 8px;
    }
  }

  .button-row {
    display: flex;
    gap: 16px;
    flex-direction: column;
    align-items: stretch;

    @media (min-width: 768px) {
      flex-direction: row;
      align-items: center;
    }
  }

  .event-log {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 16px;

    .log-item {
      display: flex;
      gap: 12px;
      padding: 4px 0;
      border-bottom: 1px solid #e9ecef;
      font-size: 13px;

      &:last-child {
        border-bottom: none;
      }

      .log-time {
        color: #6c757d;
        font-family: monospace;
        min-width: 80px;
      }

      .log-event {
        color: #495057;
        flex: 1;
      }
    }

    .no-logs {
      text-align: center;
      color: #6c757d;
      font-style: italic;
      padding: 20px;
    }
  }

  .log-controls {
    display: flex;
    justify-content: center;

    .clear-btn {
      padding: 8px 16px;
      background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.2s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
      }

      &:active {
        transform: translateY(0);
      }
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .gradient-demo {
    padding: 16px;

    .demo-section {
      padding: 16px;
    }

    .button-row {
      gap: 12px;
    }
  }
}
</style>
