<template>
  <ZPage title="PublicActivityBonusTip组件测试" backgroundColor="#f5f5f5">
    <div class="test-container">
      <h2>PublicActivityBonusTip组件测试页面</h2>

      <div class="test-section">
        <h3>基础功能测试</h3>
        <div class="button-group">
          <button @click="showNormalBonus" class="test-btn primary">显示普通奖励</button>
          <button @click="showAdjustmentBonus" class="test-btn secondary">显示调账奖励</button>
          <button @click="showRegisterBonus" class="test-btn success">显示注册奖励</button>
          <button @click="showBindPhoneBonus" class="test-btn info">显示绑定手机奖励</button>
          <button @click="showLargeAmountBonus" class="test-btn tertiary">显示大额奖励</button>
          <button @click="showMultipleBonuses" class="test-btn quaternary">显示多个奖励</button>
        </div>
      </div>

      <div class="test-section">
        <h3>当前状态</h3>
        <div class="status-info">
          <p>
            <strong>活动奖励弹窗:</strong>
            {{ autoPopMgrStore.showActivityBonusTip ? "显示中" : "已隐藏" }}
          </p>
          <p>
            <strong>注册奖励弹窗:</strong>
            {{ autoPopMgrStore.showRegisterBonusTip ? "显示中" : "已隐藏" }}
          </p>
          <p><strong>奖励队列长度:</strong> {{ autoPopMgrStore.popActivityBonus.length }}</p>
          <p><strong>当前奖励类型:</strong> {{ currentBonusType || "无" }}</p>
          <p><strong>当前奖励金额:</strong> {{ currentBonusAmount || "无" }}</p>
          <p><strong>注册奖励类型:</strong> {{ autoPopMgrStore.activityBonusType }}</p>
          <p><strong>注册奖励金额:</strong> {{ globalStore.registerAward?.amount || "无" }}</p>
          <p><strong>动画触发次数:</strong> {{ animationCount }}</p>
        </div>
      </div>

      <div class="test-section">
        <h3>事件日志</h3>
        <div class="event-log">
          <div v-for="(log, index) in eventLogs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-event">{{ log.event }}</span>
            <span class="log-data">{{ log.data }}</span>
          </div>
          <div v-if="eventLogs.length === 0" class="no-logs">暂无事件日志</div>
        </div>
      </div>

      <div class="test-section">
        <h3>控制面板</h3>
        <div class="control-panel">
          <button @click="clearQueue" class="test-btn danger">清空奖励队列</button>
          <button @click="clearLogs" class="test-btn warning">清空日志</button>
          <button @click="forceClose" class="test-btn info">强制关闭弹窗</button>
        </div>
      </div>
    </div>

    <!-- PublicActivityBonusTip 组件 -->
    <PublicActivityBonusTip @start-coin-animation="handleCoinAnimation" />
  </ZPage>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { storeToRefs } from "pinia";
import { useAutoPopMgrStore } from "@/stores/autoPopMgr";
import { useGlobalStore } from "@/stores/global";
import { AWARD_UPDATE_TYPE } from "@/utils/config/GlobalConstant";
// @ts-ignore
import PublicActivityBonusTip from "@/components/ZPopDialog/PublicActivityBonusTip.vue";

// Store
const autoPopMgrStore = useAutoPopMgrStore();
const globalStore = useGlobalStore();

// 响应式数据
const animationCount = ref(0);
const eventLogs = ref<Array<{ time: string; event: string; data: string }>>([]);

// 计算属性
const showDialog = computed(() => autoPopMgrStore.showActivityBonusTip);
const currentBonusType = computed(() => {
  const firstBonus = autoPopMgrStore.popActivityBonus?.[0] as any;
  if (!firstBonus) return null;
  return firstBonus.activity_name || firstBonus.type || "未知类型";
});
const currentBonusAmount = computed(() => {
  const firstBonus = autoPopMgrStore.popActivityBonus?.[0] as any;
  return firstBonus?.bonus || null;
});

// 添加事件日志
const addLog = (event: string, data: string = "") => {
  const now = new Date();
  const time = now.toLocaleTimeString();
  eventLogs.value.unshift({ time, event, data });
  // 只保留最近20条日志
  if (eventLogs.value.length > 20) {
    eventLogs.value = eventLogs.value.slice(0, 20);
  }
};

// 创建奖励数据
const createBonusData = (type: string, bonus: number, options: any = {}) => {
  return {
    id: Date.now() + Math.random(),
    type: type,
    bonus: bonus,
    bonus_date: new Date().toISOString().split("T")[0],
    activity_name: options.activity_name || null,
    ...options,
  };
};

// 显示普通奖励
const showNormalBonus = () => {
  const bonusData = createBonusData("REGISTER_BONUS", 1000);
  (autoPopMgrStore.popActivityBonus as any) = [bonusData];
  autoPopMgrStore.showActivityBonusTip = true;
  addLog("显示普通奖励", `类型: ${bonusData.type}, 金额: ${bonusData.bonus}`);
};

// 显示调账奖励
const showAdjustmentBonus = () => {
  const bonusData = createBonusData("ADJUSTMENT_BONUS", 2500, {
    activity_name: "Special Promotion",
  });
  (autoPopMgrStore.popActivityBonus as any) = [bonusData];
  autoPopMgrStore.showActivityBonusTip = true;
  addLog("显示调账奖励", `活动: ${bonusData.activity_name}, 金额: ${bonusData.bonus}`);
};

// 显示注册奖励
const showRegisterBonus = () => {
  // 设置注册奖励数据
  globalStore.updateRegisterAward({ type: 1, amount: 30 });
  autoPopMgrStore.activityBonusType = AWARD_UPDATE_TYPE.REGISTER_USER;
  autoPopMgrStore.showRegisterBonusTip = true;
  addLog("显示注册奖励", `类型: 新用户注册, 金额: 30`);
};

// 显示绑定手机奖励
const showBindPhoneBonus = () => {
  // 设置绑定手机奖励数据
  globalStore.updateRegisterAward({ type: 2, amount: 50 });
  autoPopMgrStore.activityBonusType = AWARD_UPDATE_TYPE.BING_IPHONE_USER;
  autoPopMgrStore.showRegisterBonusTip = true;
  addLog("显示绑定手机奖励", `类型: 绑定手机, 金额: 50`);
};

// 显示大额奖励
const showLargeAmountBonus = () => {
  const bonusData = createBonusData("VIP_BONUS", 50000);
  (autoPopMgrStore.popActivityBonus as any) = [bonusData];
  autoPopMgrStore.showActivityBonusTip = true;
  addLog("显示大额奖励", `类型: ${bonusData.type}, 金额: ${bonusData.bonus}`);
};

// 显示多个奖励
const showMultipleBonuses = () => {
  const bonuses = [
    createBonusData("DAILY_BONUS", 500),
    createBonusData("WEEKLY_BONUS", 1500),
    createBonusData("MONTHLY_BONUS", 5000, { activity_name: "Monthly Special" }),
  ];
  (autoPopMgrStore.popActivityBonus as any) = bonuses;
  autoPopMgrStore.showActivityBonusTip = true;
  addLog("显示多个奖励", `队列长度: ${bonuses.length}`);
};

// 处理金币动画
const handleCoinAnimation = (element: HTMLElement) => {
  animationCount.value++;
  addLog("金币动画触发", `第${animationCount.value}次`);
};

// 清空奖励队列
const clearQueue = () => {
  (autoPopMgrStore.popActivityBonus as any) = [];
  autoPopMgrStore.showActivityBonusTip = false;
  autoPopMgrStore.showRegisterBonusTip = false;
  globalStore.updateRegisterAward({ type: 0, amount: 0 });
  autoPopMgrStore.activityBonusType = -1;
  addLog("清空奖励队列");
};

// 清空日志
const clearLogs = () => {
  eventLogs.value = [];
  addLog("清空日志");
};

// 强制关闭弹窗
const forceClose = () => {
  autoPopMgrStore.showActivityBonusTip = false;
  autoPopMgrStore.showRegisterBonusTip = false;
  addLog("强制关闭弹窗");
};

// 组件挂载时初始化
onMounted(() => {
  addLog("组件初始化完成");
});
</script>

<style scoped lang="scss">
.test-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;

  h2 {
    color: #333;
    text-align: center;
    margin-bottom: 30px;
    font-size: 24px;
  }

  .test-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h3 {
      color: #555;
      margin-bottom: 15px;
      font-size: 18px;
      border-bottom: 2px solid #f0f0f0;
      padding-bottom: 8px;
    }
  }

  .button-group,
  .control-panel {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;

    .test-btn {
      padding: 12px 24px;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 600;
      transition: all 0.2s ease;

      &.primary {
        background: linear-gradient(135deg, #ffb800 0%, #ff8a00 100%);
        color: white;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(255, 184, 0, 0.3);
        }
      }

      &.secondary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
      }

      &.tertiary {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(240, 147, 251, 0.3);
        }
      }

      &.quaternary {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);
        }
      }

      &.success {
        background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
        color: white;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 184, 148, 0.3);
        }
      }

      &.danger {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        color: white;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
        }
      }

      &.warning {
        background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
        color: white;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(254, 202, 87, 0.3);
        }
      }

      &.info {
        background: linear-gradient(135deg, #54a0ff 0%, #5f27cd 100%);
        color: white;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(84, 160, 255, 0.3);
        }
      }

      &:active {
        transform: translateY(0);
      }
    }
  }

  .status-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;

    p {
      margin: 8px 0;
      font-size: 14px;

      strong {
        color: #333;
        font-weight: 600;
      }
    }
  }

  .event-log {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    max-height: 300px;
    overflow-y: auto;

    .log-item {
      display: flex;
      gap: 12px;
      padding: 8px 0;
      border-bottom: 1px solid #e9ecef;
      font-size: 13px;

      &:last-child {
        border-bottom: none;
      }

      .log-time {
        color: #6c757d;
        font-family: monospace;
        min-width: 80px;
      }

      .log-event {
        color: #495057;
        font-weight: 600;
        min-width: 120px;
      }

      .log-data {
        color: #6c757d;
        word-break: break-all;
        flex: 1;
      }
    }

    .no-logs {
      text-align: center;
      color: #6c757d;
      font-style: italic;
      padding: 20px;
    }
  }
}

// 响应式适配
@media (max-width: 768px) {
  .test-container {
    padding: 16px;

    .button-group,
    .control-panel {
      flex-direction: column;

      .test-btn {
        width: 100%;
      }
    }

    .event-log .log-item {
      flex-direction: column;
      gap: 4px;

      .log-time,
      .log-event {
        min-width: auto;
      }
    }
  }
}
</style>
