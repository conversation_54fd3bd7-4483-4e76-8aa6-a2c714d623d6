<template>
  <div class="game-item-wrapper" v-if="shouldShowGame">
    <!-- 游戏标签 -->
    <div v-if="game.tags" class="game-item-tag">
      <img v-if="game.tags == 1" class="icon" src="@/assets/icons/categories/Hot.png" />
      <img v-else-if="game.tags == 2" class="icon" src="@/assets/icons/categories/New.png" />
      <img v-else class="icon" src="@/assets/icons/categories/Promo.png" />
    </div>
    <div class="game-item" @click="jumpTo">
      <div class="game-item-img">
        <!-- 维护遮罩 -->
        <div class="overlay" v-show="isGameInMaintenance">
          <span>MAINTENANCE</span>
        </div>
        <!-- 游戏图片 -->
        <ZImage :src="gameImageSrc" />
        <!-- 金额范围 -->
        <div
          v-show="game.table_group > 0 && game.min_display_amount && game.max_display_amount"
          class="game-item-amountRange"
        >
          ₱ {{ tranNumberToK(game.min_display_amount) }} -
          {{ tranNumberToK(game.max_display_amount) }}
        </div>
        <!-- 喜欢按钮 -->
        <div @click.stop.prevent="handleLike" :class="`game-item-like ${likeIconClass}`">
          <ZIcon type="icon-aixin" :size="20" :color="likeIconColor" />
        </div>
      </div>
      <!-- 游戏公司 -->
      <div v-if="gameCompanyShortName" class="game-item-company">
        <span :class="{ 'small-text': gameCompanyShortName.length > 5 }">
          {{ gameCompanyShortName }}
        </span>
      </div>
      <div class="game-item-name">
        <div class="name" v-html="formatGameName(game.name)"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import {
  useGameStore,
  isGameHidden,
  isGameInMaintenance as checkGameMaintenance,
} from "@/stores/game";
import { storeToRefs } from "pinia";
import { jumpGame } from "@/utils/JumpGame";
import { likeGame } from "@/api/games";
import { showToast } from "vant";
import { getToken } from "@/utils/auth";
import router from "@/router";
import { getServerSideImageUrl } from "@/utils/core/tools";

// 定义游戏对象类型
interface GameItem {
  id: number;
  name: string;
  images: string;
  big_images?: string;
  big_images_set?: number;
  tags?: number;
  is_like: string | number | boolean;
  company_id: number;
  table_group?: number;
  min_display_amount?: number;
  max_display_amount?: number;
}

// Props 定义
const props = defineProps<{
  game: GameItem;
}>();

// Emits 定义
const emit = defineEmits<{
  updateLike: [game: GameItem];
}>();

// Store
const gameStore = useGameStore();
const { allThirdCompany, maintenanceList, hideList } = storeToRefs(gameStore);

// 喜欢图标映射函数
const getLikeIconClass = (isLike: string | number | boolean): string => {
  const normalizedValue = String(isLike);
  return normalizedValue === "1" || normalizedValue === "true" ? "like_active" : "like";
};
// 数字转义
const tranNumberToK = (num: number) => {
  return num > 1000 ? `${Math.floor(num / 1000)}K` : num;
};

// 计算属性
const gameCompanyShortName = computed(() => {
  const company = allThirdCompany.value?.find(
    (item) => String(item.id) === String(props.game.company_id)
  );
  return company ? company.short_name || company.provider : "";
});

// 游戏图片
const gameImageSrc = computed(() => {
  const { game } = props;
  const path = game.big_images_set !== 0 ? game.big_images : game.images;
  return getServerSideImageUrl(path || game.images);
});

const likeIconClass = computed(() => {
  return getLikeIconClass(props.game.is_like);
});

// 维护
const isGameInMaintenance = computed(() => {
  return checkGameMaintenance(props.game.id, maintenanceList.value);
});

// 隐藏
const shouldShowGame = computed(() => {
  return !isGameHidden(props.game.id, hideList.value);
});
// 喜欢
const likeIconColor = computed(() => {
  return likeIconClass.value !== "like_active" ? "#999" : "#fff";
});
// 函数
const handleLike = async (e: Event) => {
  e.preventDefault();
  if (!getToken()) {
    router.push("/login");
    return;
  }

  try {
    const res = await likeGame({
      game_id: props.game.id,
      is_like: props.game.is_like == "1" ? 0 : 1,
    });
    const message =
      res?.is_like == "1"
        ? "Successfully added to favorite list"
        : "Successfully removed from favorite list";

    showToast(message);
    emit("updateLike", { ...props.game, ...res });
  } catch (error) {
    console.error("Failed to update like status:", error);
    showToast("Operation failed, please try again");
  }
};

const jumpTo = async () => {
  try {
    await jumpGame(props.game);
  } catch (error) {
    console.error("Failed to jump to game:", error);
  }
};

/**
 * 格式化游戏名称
 * 将 | 符号替换为换行符
 * @param name 游戏名称
 * @returns 格式化后的 HTML 字符串
 */
const formatGameName = (name: string): string => {
  if (!name) return "";

  // 将 | 替换为 <br> 标签，实现换行效果
  return name.replace(/\|/g, "<br>");
};
</script>

<style scoped lang="scss">
.game-item-wrapper {
  position: relative;
  overflow: visible;
  padding: 2px 0 0 2px;
  transition: all 0.2s ease;
  cursor: pointer;
  // transform: scale(1); /* 默认状态，确保恢复原样 */

  &:hover {
    // transform: scale(1.1);
  }

  &:active {
    transform: scale(1.1);
  }

  /* 确保不活动时恢复原状态 */
  &:not(:hover):not(:active) {
    transform: scale(1);
  }
}

.game-item-tag {
  display: inline-block;
  position: absolute;
  top: 0px;
  left: 0px;
  z-index: 4; // 在维护遮罩层上面

  .icon {
    width: 40px;
    height: 40px;
  }
}

.game-item {
  position: relative;
  background-color: #fff;
  border-radius: 16px;
  transition: all 0.2s ease;

  &:hover {
    // background-color: #f5f5f5;
  }

  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
    background: rgba(0, 0, 0, 0.4);
    color: #fff;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
  }

  .game-item-img {
    position: relative;
    border-radius: 16px;
    overflow: hidden;
    box-sizing: border-box;

    .game-item-like {
      position: absolute;
      bottom: 5px;
      right: 5px;
      z-index: 4;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
      box-sizing: content-box;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      background-color: rgba(255, 255, 255, 0.8);

      &.like_active {
        background: linear-gradient(136deg, #ff9b67 30%, #ff3961);
      }
    }
  }

  .game-item-amountRange {
    position: absolute;
    bottom: 0;
    left: 0;
    padding: 4px 8px;
    font-size: 10px;
    border-radius: 0 12px 12px 0;
    background-color: #4880ed;
    color: #fff;
  }

  .game-item-company {
    min-width: 40px;
    height: 20px;
    position: absolute;
    top: 1px;
    right: 0;
    flex-shrink: 0;
    padding: 4px 4px 4px 8px;
    background: linear-gradient(90deg, rgba(0, 0, 0, 0) 0%, #000 100%);
    font-size: 10px;
    color: #fff;
    text-align: center;
    border-radius: 0px 16px 0px 0px;
    font-weight: 500;
    z-index: 4;

    .small-text {
      display: inline-block;
      transform: scale(0.7);
    }
  }

  .game-item-name {
    display: flex;
    flex-direction: column;
    justify-content: center;
    // padding: 6px 6px;
    padding: 0 6px;
    color: #111;
    font-size: 12px;
    font-weight: 500;
    overflow: hidden;
    height: 46px;

    .name {
      line-height: 1.2;
      // height: 24px;
      width: 100%;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      /* 标准属性 */
      /* 限制最多2行 */
      -webkit-box-orient: vertical;
      overflow: hidden;
      color: #000;
      font-family: Inter;
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }
  }
}
</style>
