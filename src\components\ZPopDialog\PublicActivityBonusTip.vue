<template>
  <!-- 通用奖励弹窗样式组件：反水奖励、注册奖励等 -->
  <ZPopOverlay :show="props.show">
    <div class="wrap" :style="{ backgroundImage: `url(${lightBgImg})` }">
      <!-- 顶部礼盒图片 -->
      <img class="head-gift" :src="giftImg" alt="" />
      <!-- 内容外框-border显示 -->
      <div class="content-border">
        <!-- 内容 -->
        <div class="content">
          <!-- 标题 -->
          <img class="head-title" :src="congratulationsImg" alt="" />
          <div>
            <!-- 提示文本 -->
            <div class="tips">{{ props.tipText }}</div>
            <!-- 金额 -->
            <div class="bonus-wrap">
              <IconCoin class="icon" :size="40" />
              <span class="bonus">{{ props.bonusAmount }}</span>
            </div>
          </div>
          <div class="btn" ref="confirmBtnRef">
            <GradientButton
              background-gradient="linear-gradient(180deg, #FF1E35 20.59%, #FF916C 94.85%)"
              border-gradient="#FFDFBF"
              :showLight="true"
              @click="handleConfirm"
              >{{ props.buttonText }}</GradientButton
            >
          </div>
          <div class="date" v-if="props.dateText">{{ props.dateText }}</div>
        </div>
      </div>
    </div>
  </ZPopOverlay>
</template>

<script lang="ts" setup>
import { ref } from "vue";
// 导入默认图片资源
import giftImg from "@/assets/images/popDialog/activityBonus-headGift.png";
import lightBgImg from "@/assets/images/popDialog/activityBonus-light.png";
import congratulationsImg from "@/assets/images/popDialog/activityBonus-congratulations.png";

// Props 定义
interface Props {
  show: boolean;
  tipText: string;
  bonusAmount: string;
  dateText?: string;
  buttonText?: string;
}

const props = withDefaults(defineProps<Props>(), {
  buttonText: "Done",
});

const emit = defineEmits<{
  confirm: [element: HTMLElement | null];
}>();

const confirmBtnRef = ref<HTMLElement | null>(null);

const handleConfirm = () => {
  emit("confirm", confirmBtnRef.value);
};

defineExpose({
  confirmBtnRef,
});
</script>

<style lang="scss" scoped>
.wrap {
  width: 100%;
  text-align: center;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 100% 100%;
  position: relative;
  font-family: "Inter";
  .head-gift {
    width: 160px;
    height: auto;
    margin: 0 auto;
    position: absolute;
    top: 95px;
    left: 30%;
  }
  .head-title {
    width: 220px;
    height: auto;
    margin: 0 auto 20px;
  }
  .content-border {
    background: #fff6c6;
    padding: 2px;
    margin: 150px 24px 90px;
    // min-height: 300px;
    border-radius: 33px;
    .content {
      border-radius: 33px;
      padding: 73px 21px 25px;
      background: linear-gradient(180deg, #ffd0d0cb 0%, #fff 30%);
      height: 100%;
      // min-height: 298px;
      width: 100%;
    }
  }
  .tips {
    color: #222;
    text-align: left;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 28px;
  }
  .bonus-wrap {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    margin-top: 16px;
    line-height: 1;
    .bonus {
      color: #222;
      font-family: "D-DIN";
      font-size: 36px;
      font-style: normal;
      font-weight: 700;
    }
  }

  .btn {
    margin-top: 36px;
  }
  .date {
    margin-top: 8px;
    color: #222;
    text-align: center;
    font-size: 16px;
    font-weight: 400;
    line-height: normal;
  }
}
</style>
